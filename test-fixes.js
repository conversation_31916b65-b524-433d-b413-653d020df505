#!/usr/bin/env node

/**
 * 测试修复效果的脚本
 */

const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TESTS = [
  {
    name: '首页加载测试',
    path: '/',
    expectedStatus: 200,
    timeout: 10000
  },
  {
    name: '数据库配置API测试',
    path: '/api/config/databases',
    expectedStatus: 200,
    timeout: 5000
  },
  {
    name: '全局搜索API测试',
    path: '/api/global-search?q=test',
    expectedStatus: 200,
    timeout: 5000
  },
  {
    name: '数据库页面测试 (us_class)',
    path: '/data/list/us_class',
    expectedStatus: 200,
    timeout: 10000
  },
  {
    name: '数据库页面测试 (us_pmn)',
    path: '/data/list/us_pmn',
    expectedStatus: 200,
    timeout: 10000
  }
];

// 执行HTTP请求的Promise包装
function makeRequest(url, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error(`Request timeout after ${timeout}ms`));
    }, timeout);

    const req = http.get(url, (res) => {
      clearTimeout(timer);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      clearTimeout(timer);
      reject(err);
    });
  });
}

// 运行单个测试
async function runTest(test) {
  const url = `${BASE_URL}${test.path}`;
  console.log(`\n🧪 Testing: ${test.name}`);
  console.log(`   URL: ${url}`);
  
  try {
    const startTime = Date.now();
    const response = await makeRequest(url, test.timeout);
    const duration = Date.now() - startTime;
    
    if (response.status === test.expectedStatus) {
      console.log(`   ✅ PASS - Status: ${response.status}, Duration: ${duration}ms`);
      return { success: true, duration, status: response.status };
    } else {
      console.log(`   ❌ FAIL - Expected: ${test.expectedStatus}, Got: ${response.status}, Duration: ${duration}ms`);
      return { success: false, duration, status: response.status, error: `Status mismatch` };
    }
  } catch (error) {
    console.log(`   ❌ ERROR - ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 Starting application tests...\n');
  
  const results = [];
  let passCount = 0;
  let failCount = 0;
  
  for (const test of TESTS) {
    const result = await runTest(test);
    results.push({ test: test.name, ...result });
    
    if (result.success) {
      passCount++;
    } else {
      failCount++;
    }
    
    // 短暂延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 输出总结
  console.log('\n📊 Test Summary:');
  console.log(`   Total: ${TESTS.length}`);
  console.log(`   Passed: ${passCount}`);
  console.log(`   Failed: ${failCount}`);
  
  if (failCount === 0) {
    console.log('\n🎉 All tests passed! The fixes appear to be working.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
  
  // 输出详细结果
  console.log('\n📋 Detailed Results:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const duration = result.duration ? ` (${result.duration}ms)` : '';
    console.log(`   ${status} ${result.test}${duration}`);
    if (result.error) {
      console.log(`      Error: ${result.error}`);
    }
  });
  
  return failCount === 0;
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await makeRequest(BASE_URL, 3000);
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the development server first:');
    console.log('   npm run dev');
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔍 Checking if server is running...');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    process.exit(1);
  }
  
  console.log('✅ Server is running, starting tests...');
  
  const allPassed = await runAllTests();
  process.exit(allPassed ? 0 : 1);
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test runner error:', error);
    process.exit(1);
  });
}
