"use client";

import { ReactNode, useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth';
import AccessRestrictedAlert from './AccessRestrictedAlert';

interface SimpleAccessCheckProps {
  database: string;
  children: ReactNode;
}

/**
 * 动态权限检查组件
 * 从数据库配置中读取权限要求，而不是硬编码
 */
export default function SimpleAccessCheck({ database, children }: SimpleAccessCheckProps) {
  const { user } = useAuth();
  const [accessLevel, setAccessLevel] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // 动态获取数据库访问级别
  useEffect(() => {
    const fetchAccessLevel = async () => {
      try {
        const response = await fetch('/api/config/databases');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data[database]) {
            const dbConfig = result.data[database];
            setAccessLevel(dbConfig.accessLevel || 'premium');
            console.log(`🔍 [Dynamic Permission] ${database} requires: ${dbConfig.accessLevel || 'premium'}`);
          } else {
            // 数据库不存在，设置为特殊状态
            setAccessLevel('not_found');
            console.log(`⚠️ [Dynamic Permission] ${database} not found in config - database may have been removed`);
          }
        } else {
          // API失败，设置为错误状态
          setAccessLevel('api_error');
          console.log(`⚠️ [Dynamic Permission] API failed for ${database}`);
        }
      } catch (error) {
        // 网络错误，设置为错误状态
        setAccessLevel('api_error');
        console.log(`⚠️ [Dynamic Permission] Network error for ${database}:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchAccessLevel();
  }, [database]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-500">Checking permissions...</div>
      </div>
    );
  }

  // 数据库不存在
  if (accessLevel === 'not_found') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Database Not Found</h1>
          <p className="text-gray-600 mb-6">
            The database "{database}" does not exist or has been removed.
          </p>
          <div className="space-x-4">
            <a href="/" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Back to Home
            </a>
            <a href="/data" className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              Browse Databases
            </a>
          </div>
        </div>
      </div>
    );
  }

  // API错误
  if (accessLevel === 'api_error') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Service Temporarily Unavailable</h1>
          <p className="text-gray-600 mb-6">
            Unable to verify database access. Please try again later.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // 免费数据库：所有人都可以访问（包括未登录用户）
  if (accessLevel === 'free') {
    console.log('✅ [Dynamic Permission] Free database, access allowed:', database);
    return <>{children}</>;
  }

  // 需要高级权限的数据库
  if (accessLevel === 'premium' || accessLevel === 'enterprise') {
    // 未登录用户
    if (!user) {
      return (
        <AccessRestrictedAlert
          databaseCode={database}
          requiredLevel={accessLevel as any}
        />
      );
    }

    // 检查会员级别
    const userLevel = user.membershipType;
    const hasAccess =
      (accessLevel === 'premium' && (userLevel === 'premium' || userLevel === 'enterprise')) ||
      (accessLevel === 'enterprise' && userLevel === 'enterprise');

    if (hasAccess) {
      console.log(`✅ [Dynamic Permission] User has ${userLevel} access for ${database} (requires ${accessLevel})`);
      return <>{children}</>;
    }

    // 权限不足
    return (
      <AccessRestrictedAlert
        databaseCode={database}
        requiredLevel={accessLevel as any}
      />
    );
  }

  // 默认情况：允许访问（对于未知访问级别）
  console.log('✅ [Dynamic Permission] Unknown access level, allowing access:', database, accessLevel);
  return <>{children}</>;
}